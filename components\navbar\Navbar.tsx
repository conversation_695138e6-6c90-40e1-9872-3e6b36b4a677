"use client";

import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { Menu, X, LogOut } from "lucide-react";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";

function CustomIcon({ src, alt, className }: { src: string; alt: string; className?: string }) {
  return (
    <Image
      src={src}
      alt={alt}
      width={28}
      height={28}
      className={className}
    />
  );
}

interface SubItem {
  label: string;
  href: string;
  iconSrc?: string;
  description?: string;
}

interface NavItem {
  label: string;
  href?: string;
  iconSrc?: string;
  subItems?: SubItem[];
}

// Base navigation items (without auth-dependent items)
const baseNavigationItems: NavItem[] = [
  {
    label: "Home",
    href: "/",
    iconSrc: "/home.svg",
  },
  {
    label: "Story Books",
    iconSrc: "/fairy-tale-book.svg",
    subItems: [
      {
        label: "All Books",
        href: "/available-books",
        iconSrc: "/game-book.svg",
        description: "Find all books"
      },
      {
        label: "My Books",
        href: "/my-books",
        iconSrc: "/category.svg",
        description: "My favourite books"
      },
    ],
  },
];

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Check for user email in localStorage on component mount
  useEffect(() => {
    const email = localStorage.getItem('userEmail');
    setUserEmail(email);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('userEmail');
    setUserEmail(null);
    window.location.href = '/';
  };

  // Create dynamic navigation items based on login status
  const getNavigationItems = (): NavItem[] => {
    const items = [...baseNavigationItems];

    if (userEmail) {
      // User is logged in, show logout button
      items.push({
        label: "Log Out",
        href: "#",
        iconSrc: "/logout.svg", // You can use any logout icon or create a custom one
      });
    } else {
      // User is not logged in, show sign in button
      items.push({
        label: "Sign In",
        href: "/login",
        iconSrc: "/login-13.png",
      });
    }

    return items;
  };

  const navigationItems = getNavigationItems();

  return (
    <header className="bg-gradient-to-r from-sky-1 to-cyan-1 sticky top-0 z-40 w-full shadow-sm">
      <div className="container flex h-24 md:h-28 lg:h-32 items-center px-4 md:px-6">
        {/* Logo Section */}
        <Link 
          href="/" 
          className="mr-4 md:mr-8 p-2 lg:p-0 lg:ml-10 group hover:scale-105 transition-transform duration-300 focus:outline-none focus:ring-4 focus:ring-sky-300 rounded-xl"
          aria-label="KinderLib Home - Fun stories for every kid"
        >
          <Image
            src="/logo/kinderlib-logo.svg"
            alt="KinderLib Logo"
            width={300}
            height={300}
            className="p-2 lg:p-0 h-32 w-32 md:h-32 md:w-32 lg:h-36 lg:w-36"
          />
        </Link>
        
        {/* Desktop Navigation */}
        <NavigationMenu className="hidden lg:flex ml-auto">
          <NavigationMenuList>
            {navigationItems.map((item) => (
              <NavigationMenuItem key={item.label}>
                {item.subItems ? (
                  <>
                    <NavigationMenuTrigger className="text-xl font-medium font-quicksand h-auto py-4 px-6">
                      <div className="flex items-center space-x-3">
                        {item.iconSrc && (
                          <CustomIcon 
                            src={item.iconSrc} 
                            alt={`${item.label} icon`} 
                            className="w-7 h-7" 
                          />
                        )}
                        <span>{item.label}</span>
                      </div>
                    </NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <div className="grid w-80 gap-3 p-4">
                        {item.subItems.map((subItem) => (
                          <NavigationMenuLink key={subItem.label} asChild>
                            <Link
                              href={subItem.href}
                              className="flex items-start space-x-4 select-none rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            >
                              {subItem.iconSrc && (
                                <CustomIcon 
                                  src={subItem.iconSrc} 
                                  alt={`${subItem.label} icon`} 
                                  className="w-8 h-8 mt-0.5" 
                                />
                              )}
                              <div className="space-y-1">
                                <div className="text-sm font-medium leading-none">
                                  {subItem.label}
                                </div>
                                {subItem.description && (
                                  <p className="line-clamp-2 text-xs leading-snug text-muted-foreground">
                                    {subItem.description}
                                  </p>
                                )}
                              </div>
                            </Link>
                          </NavigationMenuLink>
                        ))}
                      </div>
                    </NavigationMenuContent>
                  </>
                ) : (
                  <NavigationMenuLink asChild>
                    {item.label === "Log Out" ? (
                      <button
                        onClick={handleLogout}
                        className={cn(navigationMenuTriggerStyle(), "text-xl font-medium font-quicksand h-auto py-4 px-6 cursor-pointer")}
                      >
                        <div className="flex items-center space-x-3">
                          <LogOut className="w-7 h-7" />
                          <span>{item.label}</span>
                        </div>
                      </button>
                    ) : (
                      <Link href={item.href!} className={cn(navigationMenuTriggerStyle(), "text-xl font-medium font-quicksand h-auto py-4 px-6")}>
                        <div className="flex items-center space-x-3">
                          {item.iconSrc && (
                            <CustomIcon
                              src={item.iconSrc}
                              alt={`${item.label} icon`}
                              className="w-7 h-7"
                            />
                          )}
                          <span>{item.label}</span>
                        </div>
                      </Link>
                    )}
                  </NavigationMenuLink>
                )}
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Mobile Menu Button */}
        <div className="ml-auto lg:hidden">
          <button 
            onClick={toggleMobileMenu}
            className="p-3 rounded-xl bg-sky-100 hover:bg-sky-200 transition-colors focus:outline-none focus:ring-4 focus:ring-sky-300"
            aria-label={isMobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
            aria-expanded={isMobileMenuOpen}
          >
            {isMobileMenuOpen ? (
              <X className="h-8 w-8 text-sky-600" />
            ) : (
              <Menu className="h-8 w-8 text-sky-600" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
          <div className="container px-4 py-6">
            <nav className="space-y-3" role="navigation" aria-label="Mobile navigation">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  {item.subItems ? (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-4 px-6 py-3 text-xl font-medium text-gray-700 font-quicksand">
                        {item.iconSrc && (
                          <CustomIcon 
                            src={item.iconSrc} 
                            alt={`${item.label} icon`} 
                            className="w-7 h-7" 
                          />
                        )}
                        <span>{item.label}</span>
                      </div>
                      <div className="ml-6 space-y-2">
                        {item.subItems.map((subItem) => (
                          <Link
                            key={subItem.label}
                            href={subItem.href}
                            onClick={closeMobileMenu}
                            className="flex items-center space-x-4 px-5 py-3 text-gray-600 hover:text-sky-600 hover:bg-sky-50 rounded-lg transition-colors duration-150 text-lg"
                          >
                            {subItem.iconSrc && (
                              <CustomIcon 
                                src={subItem.iconSrc} 
                                alt={`${subItem.label} icon`} 
                                className="w-6 h-6" 
                              />
                            )}
                            <span>{subItem.label}</span>
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <>
                      {item.label === "Log Out" ? (
                        <button
                          onClick={() => {
                            handleLogout();
                            closeMobileMenu();
                          }}
                          className="flex items-center space-x-4 px-6 py-5 text-xl font-medium text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-xl transition-colors duration-200 font-quicksand w-full text-left"
                        >
                          <LogOut className="w-7 h-7" />
                          <span>{item.label}</span>
                        </button>
                      ) : (
                        <Link
                          href={item.href!}
                          onClick={closeMobileMenu}
                          className="flex items-center space-x-4 px-6 py-5 text-xl font-medium text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-xl transition-colors duration-200 font-quicksand"
                        >
                          {item.iconSrc && (
                            <CustomIcon
                              src={item.iconSrc}
                              alt={`${item.label} icon`}
                              className="w-7 h-7"
                            />
                          )}
                          <span>{item.label}</span>
                        </Link>
                      )}
                    </>
                  )}
                </div>
              ))}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
}