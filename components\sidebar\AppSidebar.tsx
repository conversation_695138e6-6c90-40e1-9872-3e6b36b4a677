// File: components/sidebar/Sidebar.tsx
'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  Home,
  BookOpen,
  Gamepad2,
  Palette,
  Music,
  Star,
  Settings,
  User,
  BookAIcon,
  Heart,
  History,
  Download,
  Award,
  Users,
  ChevronDown,
  ChevronRight,
  FileText,
  BookMarked,
  Trophy,
  Clock,
  Library,
  Shield,
  Bell,
  HelpCircle,
  CrownIcon,
  SubscriptIcon
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

// Define menu item type
interface MenuItem {
  id: string;
  title: string;
  url?: string;
  icon: string;
  children?: MenuItem[];
}

// Define sidebar group type
interface SidebarGroupConfig {
  id: string;
  label: string;
  items: MenuItem[];
}

// Icon mapping
const iconMap: { [key: string]: React.ElementType } = {
  Home,
  BookOpen,
  Gamepad2,
  Palette,
  Music,
  Star,
  Settings,
  User,
  BookAIcon,
  Heart,
  History,
  Download,
  Award,
  Users,
  FileText,
  BookMarked,
  Trophy,
  Clock,
  Library,
  Shield,
  Bell,
  HelpCircle,
  CrownIcon
};

// JSON-based sidebar configuration - fully dynamic groups and items
const sidebarConfig: SidebarGroupConfig[] = [
  {
    id: 'main-menu',
    label: 'Menu',
    items: [
      {
        id: 'subs',
        title: 'Subscription',
        url: '/subscription',
        icon: 'SubscriptIcon',
      },
      {
        id: 'books',
        title: 'Books',
        icon: 'BookOpen',
        children: [
          {
            id: 'available-books',
            title: 'Find Available Books',
            url: '/available-books',
            icon: 'BookAIcon',
          },
          {
            id: 'my-books',
            title: 'My Books',
            url: '/my-books',
            icon: 'Library',
          },
        ],
      },
  //     {
  //       id: 'activities',
  //       title: 'Activities',
  //       icon: 'Gamepad2',
  //       children: [
  //         {
  //           id: 'games',
  //           title: 'Games',
  //           url: '/games',
  //           icon: 'Gamepad2',
  //         },
  //         {
  //           id: 'music',
  //           title: 'Music',
  //           url: '/music',
  //           icon: 'Music',
  //         },
  //         {
  //           id: 'achievements',
  //           title: 'Achievements',
  //           url: '/achievements',
  //           icon: 'Trophy',
  //         },
  //       ],
  //     },
  //   ],
  // },
  // {
  //   id: 'tools',
  //   label: 'Tools',
  //   items: [
  //     {
  //       id: 'downloads',
  //       title: 'Downloads',
  //       url: '/downloads',
  //       icon: 'Download',
  //     },
  //     {
  //       id: 'community',
  //       title: 'Community',
  //       icon: 'Users',
  //       children: [
  //         {
  //           id: 'reading-groups',
  //           title: 'Reading Groups',
  //           url: '/groups',
  //           icon: 'Users',
  //         },
  //         {
  //           id: 'discussions',
  //           title: 'Discussions',
  //           url: '/discussions',
  //           icon: 'FileText',
  //         },
  //       ],
  //     },
    ],
  },
  {
    id: 'admin',
    label: 'Admin',
    items: [
      {
        id: 'admin',
        title: 'Book Product',
        url: '/admin/bookproduct',
        icon: 'CrownIcon',
      },
      {
        id: 'translator',
        title: 'Translator',
        url: '/admin/translator',
        icon: 'FileText',
      },
    ],
  },
];

// Footer items (can also be made dynamic if needed)
const footerItems: MenuItem[] = [
  {
    id: 'profile',
    title: 'Profile',
    url: '/profile',
    icon: 'User',
  },
  {
    id: 'settings',
    title: 'Settings',
    url: '/settings',
    icon: 'Settings',
  },
];

export function AppSidebar() {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Load expanded state from localStorage
  useEffect(() => {
    const savedExpanded = localStorage.getItem('sidebarExpanded');
    if (savedExpanded) {
      setExpandedItems(new Set(JSON.parse(savedExpanded)));
    }
  }, []);

  // Load user email from localStorage
  useEffect(() => {
    const email = localStorage.getItem('userEmail');
    setUserEmail(email);
  }, []);

  // Save expanded state to localStorage
  useEffect(() => {
    localStorage.setItem('sidebarExpanded', JSON.stringify([...expandedItems]));
  }, [expandedItems]);

  // Filter sidebar config based on user email using useMemo
  const filteredSidebarConfig = useMemo((): SidebarGroupConfig[] => {
    if (!userEmail) {
      // If no user email, show all groups except admin
      return sidebarConfig.filter(group => group.id !== 'admin');
    }

    // If user email contains "admin", show all groups
    if (userEmail.toLowerCase().includes('admin')) {
      return sidebarConfig;
    }

    // Otherwise, hide admin group
    return sidebarConfig.filter(group => group.id !== 'admin');
  }, [userEmail]);

  // Check if current path matches the item URL or any child URL
  const isActive = (item: MenuItem): boolean => {
    if (item.url && (pathname === item.url || pathname.startsWith(item.url + '/'))) {
      return true;
    }
    if (item.children) {
      return item.children.some(child => isActive(child));
    }
    return false;
  };

  // Check if item has active children
  const hasActiveChild = (item: MenuItem): boolean => {
    if (!item.children) return false;
    return item.children.some(child => isActive(child));
  };

  // Toggle expanded state
  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // Auto-expand parent if child is active
  useEffect(() => {
    const autoExpand = (items: MenuItem[]) => {
      items.forEach(item => {
        if (item.children && hasActiveChild(item)) {
          setExpandedItems(prev => new Set([...prev, item.id]));
        }
      });
    };

    // Check all groups for active children
    filteredSidebarConfig.forEach(group => {
      autoExpand(group.items);
    });
  }, [pathname, filteredSidebarConfig]);

  // Get icon component
  const getIcon = (iconName: string) => {
    return iconMap[iconName] || Home;
  };

  // Render menu item
  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const Icon = getIcon(item.icon);
    const isExpanded = expandedItems.has(item.id);
    const itemIsActive = isActive(item);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <SidebarMenuItem>
          {hasChildren ? (
            // Parent item with children
            <SidebarMenuButton
              onClick={() => toggleExpanded(item.id)}
              isActive={itemIsActive}
              className={`${level > 0 ? 'pl-2' : ''}`}
            >
              <Icon />
              <span>{item.title}</span>
              {isExpanded ? (
                <ChevronDown className="ml-auto h-4 w-4" />
              ) : (
                <ChevronRight className="ml-auto h-4 w-4" />
              )}
            </SidebarMenuButton>
          ) : (
            // Single item with URL
            <SidebarMenuButton asChild isActive={itemIsActive} className={`${level > 0 ? 'pl-2' : ''}`}>
              <Link href={item.url || '#'}>
                <Icon />
                <span>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          )}
        </SidebarMenuItem>

        {/* Render children if expanded */}
        {hasChildren && isExpanded && (
          <div className="ml-4 border-l border-sidebar-border space-y-1">
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </React.Fragment>
    );
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="px-4 py-2">
          <Link href="/">
            <h1 className="text-lg font-semibold">KinderLib</h1>
          </Link>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {/* Render dynamic groups */}
        {filteredSidebarConfig.map((group) => (
          <SidebarGroup key={group.id}>
            <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map(item => renderMenuItem(item))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      
      <SidebarFooter>
        <SidebarMenu>
          {footerItems.map((item) => {
            const Icon = getIcon(item.icon);
            return (
              <SidebarMenuItem key={item.id}>
                <SidebarMenuButton asChild isActive={isActive(item)}>
                  <Link href={item.url || '#'}>
                    <Icon />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}